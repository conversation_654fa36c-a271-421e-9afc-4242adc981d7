//+------------------------------------------------------------------+
//|                                                       CETP_EA.mq5 |
//|                        Copyright 2024, Canuck Trading Traders    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Canuck Trading Traders"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "CETP-Plus Strategy - Candle Entropy Trading Plus for XAUUSD 15m"

#include <Trade\Trade.mqh>

// Global trade object
CTrade trade;

// Input parameters - CETP-Plus Settings
input group "=== CETP-Plus Settings (极速高频) ==="
input int    InpCETPWindow = 3;              // CETP Window (2-20)
input int    InpCETPBins = 2;                // CETP Bins per Dimension (1-10)
input double InpLongThreshold = 0.001;       // Long Threshold (0.0-2.0)
input double InpShortThreshold = -0.001;     // Short Threshold (-2.0 to -0.0005)
input double InpCETPK = 0.5;                 // CETP Momentum Weight (0.1-5.0)
input double InpMomScale = 1.5;              // Momentum Scale (0.1-20.0)
input double InpBodyWeight = 1.0;            // Body Ratio Weight (0.0-2.0)
input double InpUpperWeight = 0.8;           // Upper Wick Ratio Weight (0.0-2.0)
input double InpLowerWeight = 1.2;           // Lower Wick Ratio Weight (0.0-2.0)
input double InpDecayFactor = 0.9;           // Decay Factor (0.1-0.99)

// Input parameters - Trade Settings
input group "=== Trade Settings (极速高频) ==="
input double InpMinScoreStrength = 0.0;     // Min CETP Score Strength (0.0-5.0)
input double InpStopLossPct = 0.6;           // Stop Loss % (0.1-5.0)
input double InpATRMult = 1.5;               // ATR Multiplier (0.1-5.0)
input double InpTrailMult = 2.0;             // Trailing ATR Mult (0.1-7.0)
input double InpTrailOffsetPct = 0.3;        // Trail Start Offset % (0.05-2.0)
input double InpMinPriceMoveMult = 0.0;      // Min Price Move ATR Mult (0.0-5.0)
input double InpMinVolMult = 0.0;            // Min Volume Multiplier (0.0-10.0)
input double InpVolThresholdLimit = 999.0;   // Vol Threshold Limit (0.0-1000.0)

// Global variables
double g_atr_buffer[];
double g_close_buffer[];
double g_high_buffer[];
double g_low_buffer[];
double g_open_buffer[];
double g_volume_buffer[];

// CETP calculation arrays
double g_body_arr[];
double g_upper_arr[];
double g_lower_arr[];
int g_hist[];

// Trade state variables
double g_entry_price = 0.0;
int g_bars_held = 0;
datetime g_last_bar_time = 0;

// ATR handle
int g_atr_handle;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize ATR indicator
   g_atr_handle = iATR(_Symbol, PERIOD_CURRENT, 14);
   if(g_atr_handle == INVALID_HANDLE)
   {
      Print("Failed to create ATR indicator");
      return INIT_FAILED;
   }

   // Initialize arrays
   ArrayResize(g_body_arr, InpCETPWindow);
   ArrayResize(g_upper_arr, InpCETPWindow);
   ArrayResize(g_lower_arr, InpCETPWindow);

   int hist_size = InpCETPBins * InpCETPBins * InpCETPBins;
   ArrayResize(g_hist, hist_size);

   // Initialize buffers
   ArrayResize(g_atr_buffer, 50);
   ArrayResize(g_close_buffer, 50);
   ArrayResize(g_high_buffer, 50);
   ArrayResize(g_low_buffer, 50);
   ArrayResize(g_open_buffer, 50);
   ArrayResize(g_volume_buffer, 50);

   ArraySetAsSeries(g_atr_buffer, true);
   ArraySetAsSeries(g_close_buffer, true);
   ArraySetAsSeries(g_high_buffer, true);
   ArraySetAsSeries(g_low_buffer, true);
   ArraySetAsSeries(g_open_buffer, true);
   ArraySetAsSeries(g_volume_buffer, true);

   Print("CETP-Plus EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(g_atr_handle != INVALID_HANDLE)
      IndicatorRelease(g_atr_handle);
}

//+------------------------------------------------------------------+
//| OnTick function                                                  |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new bar
   datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(current_time == g_last_bar_time)
      return;
   g_last_bar_time = current_time;

   // Update data buffers
   if(!UpdateBuffers())
      return;

   // Calculate CETP score
   double cetp_score = CalculateCETPScore();

   // Execute trading logic
   ExecuteTrading(cetp_score);
}

//+------------------------------------------------------------------+
//| Update price and indicator buffers                               |
//+------------------------------------------------------------------+
bool UpdateBuffers()
{
   // Get price data
   if(CopyClose(_Symbol, PERIOD_CURRENT, 0, 50, g_close_buffer) <= 0)
      return false;
   if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, 50, g_high_buffer) <= 0)
      return false;
   if(CopyLow(_Symbol, PERIOD_CURRENT, 0, 50, g_low_buffer) <= 0)
      return false;
   if(CopyOpen(_Symbol, PERIOD_CURRENT, 0, 50, g_open_buffer) <= 0)
      return false;
   if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 50, g_volume_buffer) <= 0)
      return false;

   // Get ATR data
   if(CopyBuffer(g_atr_handle, 0, 0, 50, g_atr_buffer) <= 0)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Calculate Simple Moving Average                                  |
//+------------------------------------------------------------------+
double CalculateSMA(const double &array[], int period, int shift = 0)
{
   if(period <= 0 || shift < 0)
      return 0.0;

   double sum = 0.0;
   for(int i = shift; i < shift + period && i < ArraySize(array); i++)
   {
      sum += array[i];
   }
   return sum / period;
}

//+------------------------------------------------------------------+
//| Calculate Standard Deviation                                     |
//+------------------------------------------------------------------+
double CalculateStdDev(const double &array[], int period, int shift = 0)
{
   if(period <= 0)
      return 0.0;

   double sma = CalculateSMA(array, period, shift);
   double sum = 0.0;

   for(int i = shift; i < shift + period && i < ArraySize(array); i++)
   {
      double diff = array[i] - sma;
      sum += diff * diff;
   }

   return MathSqrt(sum / period);
}

//+------------------------------------------------------------------+
//| Calculate Exponential Moving Average                             |
//+------------------------------------------------------------------+
double CalculateEMA(const double &array[], int period, int shift = 0)
{
   if(period <= 0 || shift >= ArraySize(array))
      return 0.0;

   double alpha = 2.0 / (period + 1.0);
   double ema = array[ArraySize(array) - 1]; // Start with oldest value

   for(int i = ArraySize(array) - 2; i >= shift; i--)
   {
      ema = alpha * array[i] + (1.0 - alpha) * ema;
   }

   return ema;
}

//+------------------------------------------------------------------+
//| Calculate CETP Score                                             |
//+------------------------------------------------------------------+
double CalculateCETPScore()
{
   if(ArraySize(g_close_buffer) < InpCETPWindow + 20)
      return 0.0;

   double epsilon = 1e-5;

   // Calculate current candle ratios
   double body_ratio = (g_close_buffer[0] - g_open_buffer[0]) / (g_high_buffer[0] - g_low_buffer[0] + epsilon) * InpBodyWeight;
   double upper_wick_ratio = (g_high_buffer[0] - MathMax(g_open_buffer[0], g_close_buffer[0])) / (g_high_buffer[0] - g_low_buffer[0] + epsilon) * InpUpperWeight;
   double lower_wick_ratio = (MathMin(g_open_buffer[0], g_close_buffer[0]) - g_low_buffer[0]) / (g_high_buffer[0] - g_low_buffer[0] + epsilon) * InpLowerWeight;

   // Update arrays with decay factor
   for(int i = 0; i < InpCETPWindow - 1; i++)
   {
      g_body_arr[i] = g_body_arr[i + 1] * InpDecayFactor;
      g_upper_arr[i] = g_upper_arr[i + 1] * InpDecayFactor;
      g_lower_arr[i] = g_lower_arr[i + 1] * InpDecayFactor;
   }
   g_body_arr[InpCETPWindow - 1] = body_ratio;
   g_upper_arr[InpCETPWindow - 1] = upper_wick_ratio;
   g_lower_arr[InpCETPWindow - 1] = lower_wick_ratio;

   // Calculate volatility scaling
   double atr_sma = CalculateSMA(g_atr_buffer, 14);
   double bin_size = 2.0 / InpCETPBins * (1 + (atr_sma > 0 ? g_atr_buffer[0] / atr_sma : 1.0));

   // Initialize histogram
   int hist_size = InpCETPBins * InpCETPBins * InpCETPBins;
   ArrayInitialize(g_hist, 0);

   // Fill histogram
   for(int i = 0; i < InpCETPWindow; i++)
   {
      int body_bin = (int)MathMax(0, MathMin(InpCETPBins - 1, MathFloor((g_body_arr[i] + 1) / bin_size)));
      int upper_bin = (int)MathMax(0, MathMin(InpCETPBins - 1, MathFloor((g_upper_arr[i] + 1) / bin_size)));
      int lower_bin = (int)MathMax(0, MathMin(InpCETPBins - 1, MathFloor((g_lower_arr[i] + 1) / bin_size)));
      int bin_idx = body_bin * (InpCETPBins * InpCETPBins) + upper_bin * InpCETPBins + lower_bin;
      g_hist[bin_idx]++;
   }

   // Calculate entropy
   double entropy = 0.0;
   for(int i = 0; i < hist_size; i++)
   {
      double p = (double)g_hist[i] / InpCETPWindow;
      if(p > 0)
         entropy -= p * MathLog(p);
   }

   double max_entropy = MathLog(hist_size);
   double norm_entropy = max_entropy > 0 ? entropy / max_entropy : 0.0;

   // Calculate RSI-like momentum bias
   double avg_gain = 0.0, avg_loss = 0.0;
   for(int i = 1; i < InpCETPWindow + 1; i++)
   {
      double change = g_close_buffer[i-1] - g_close_buffer[i];
      if(change > 0) avg_gain += change;
      else avg_loss += MathAbs(change);
   }
   avg_gain /= InpCETPWindow;
   avg_loss /= InpCETPWindow;
   double rsi_bias = avg_loss > 0 ? avg_gain / avg_loss : 1.0;

   // Calculate momentum
   double momentum = 0.0;
   if(g_close_buffer[InpCETPWindow] != 0)
      momentum = (g_close_buffer[0] - g_close_buffer[InpCETPWindow]) / g_close_buffer[InpCETPWindow];
   double momentum_adj = momentum * (1 + rsi_bias);

   // Calculate trend strength (ADX-like)
   double di_plus = 0.0, di_minus = 0.0;
   for(int i = 1; i < InpCETPWindow + 1; i++)
   {
      di_plus += MathMax(g_high_buffer[i-1] - g_high_buffer[i], 0);
      di_minus += MathMax(g_low_buffer[i] - g_low_buffer[i-1], 0);
   }
   if(g_atr_buffer[0] > 0)
   {
      di_plus /= (InpCETPWindow * g_atr_buffer[0]);
      di_minus /= (InpCETPWindow * g_atr_buffer[0]);
   }
   double trend_strength = (di_plus > di_minus || di_minus > di_plus) ? 1.2 : 1.0;

   // Calculate average body ratio
   double avg_body = 0.0;
   for(int i = 0; i < InpCETPWindow; i++)
      avg_body += g_body_arr[i];
   avg_body /= InpCETPWindow;

   // Final CETP score
   double raw_score = avg_body * (1 - norm_entropy) * (InpCETPK + momentum_adj * InpMomScale);
   return raw_score * trend_strength;
}

//+------------------------------------------------------------------+
//| Check trading conditions                                          |
//+------------------------------------------------------------------+
bool CheckTradingConditions(double cetp_score)
{
   // Volatility filter
   double vol_threshold = CalculateStdDev(g_close_buffer, 20) / CalculateSMA(g_close_buffer, 20) * 100;
   if(cetp_score > InpLongThreshold)
   {
      if(vol_threshold >= InpVolThresholdLimit)
         return false;
   }
   else
   {
      if(vol_threshold >= (InpVolThresholdLimit + 5.0))
         return false;
   }

   // Minimum price move filter
   double min_price_move = cetp_score > InpLongThreshold ?
      MathMax(g_atr_buffer[0] * InpMinPriceMoveMult, _Point * 10) :
      MathMax(g_atr_buffer[0] * (InpMinPriceMoveMult * 0.8), _Point * 10);

   if((g_high_buffer[0] - g_low_buffer[0]) <= min_price_move)
      return false;

   // Volume filter
   double vol_sma = CalculateSMA(g_volume_buffer, 20);
   double vol_mult = cetp_score > InpLongThreshold ? InpMinVolMult : InpMinVolMult * 0.8;
   if(g_volume_buffer[0] <= vol_sma * vol_mult)
      return false;

   // Momentum condition
   double momentum = 0.0;
   if(g_close_buffer[InpCETPWindow] != 0)
      momentum = (g_close_buffer[0] - g_close_buffer[InpCETPWindow]) / g_close_buffer[InpCETPWindow];

   double momentum_threshold = cetp_score > InpLongThreshold ? 0.05 : 0.03;
   if(MathAbs(momentum) <= momentum_threshold)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Execute trading logic                                             |
//+------------------------------------------------------------------+
void ExecuteTrading(double cetp_score)
{
   // Check minimum score strength
   if(MathAbs(cetp_score) <= InpMinScoreStrength)
      return;

   // Check trading conditions
   if(!CheckTradingConditions(cetp_score))
      return;

   // Get current position info
   bool has_long_position = false;
   bool has_short_position = false;
   double position_volume = 0.0;

   if(PositionSelect(_Symbol))
   {
      ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      position_volume = PositionGetDouble(POSITION_VOLUME);

      if(pos_type == POSITION_TYPE_BUY)
         has_long_position = true;
      else if(pos_type == POSITION_TYPE_SELL)
         has_short_position = true;
   }

   // Trading logic
   if(cetp_score > InpLongThreshold)
   {
      // Close short position if exists
      if(has_short_position)
      {
         trade.PositionClose(_Symbol);
         Print("Closed short position for reversal to long");
      }

      // Open long position if not already long
      if(!has_long_position)
      {
         double lot_size = 0.01; // You can make this configurable
         double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

         // Calculate stop loss
         double sl = ask * (1 - InpStopLossPct / 100) - g_atr_buffer[0] * InpATRMult;

         // Calculate take profit (optional - you can set to 0 for trailing only)
         double tp = 0; // Using trailing stop instead

         if(trade.Buy(lot_size, _Symbol, ask, sl, tp, "CETP Long"))
         {
            g_entry_price = ask;
            g_bars_held = 1;
            Print("Opened long position at ", ask, " with SL: ", sl);
         }
      }
   }
   else if(cetp_score < InpShortThreshold)
   {
      // Close long position if exists
      if(has_long_position)
      {
         trade.PositionClose(_Symbol);
         Print("Closed long position for reversal to short");
      }

      // Open short position if not already short
      if(!has_short_position)
      {
         double lot_size = 0.01; // You can make this configurable
         double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

         // Calculate stop loss
         double sl = bid * (1 + InpStopLossPct / 100) + g_atr_buffer[0] * InpATRMult;

         // Calculate take profit (optional - you can set to 0 for trailing only)
         double tp = 0; // Using trailing stop instead

         if(trade.Sell(lot_size, _Symbol, bid, sl, tp, "CETP Short"))
         {
            g_entry_price = bid;
            g_bars_held = 1;
            Print("Opened short position at ", bid, " with SL: ", sl);
         }
      }
   }

   // Update bars held counter
   if(has_long_position || has_short_position)
      g_bars_held++;
   else
      g_bars_held = 0;

   // Implement trailing stop logic here if needed
   UpdateTrailingStop();
}

//+------------------------------------------------------------------+
//| Update trailing stop                                             |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
   if(!PositionSelect(_Symbol))
      return;

   ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
   double position_open_price = PositionGetDouble(POSITION_OPEN_PRICE);
   double current_sl = PositionGetDouble(POSITION_SL);

   double trail_distance = g_atr_buffer[0] * InpTrailMult;
   double trail_trigger = position_open_price * (InpTrailOffsetPct / 100);

   if(pos_type == POSITION_TYPE_BUY)
   {
      double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

      // Check if price has moved enough to start trailing
      if(current_price >= position_open_price + trail_trigger)
      {
         double new_sl = current_price - trail_distance;

         // Only move SL up
         if(new_sl > current_sl)
         {
            trade.PositionModify(_Symbol, new_sl, 0);
            Print("Updated trailing stop for long position to: ", new_sl);
         }
      }
   }
   else if(pos_type == POSITION_TYPE_SELL)
   {
      double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

      // Check if price has moved enough to start trailing
      if(current_price <= position_open_price - trail_trigger)
      {
         double new_sl = current_price + trail_distance;

         // Only move SL down
         if(new_sl < current_sl || current_sl == 0)
         {
            trade.PositionModify(_Symbol, new_sl, 0);
            Print("Updated trailing stop for short position to: ", new_sl);
         }
      }
   }
}